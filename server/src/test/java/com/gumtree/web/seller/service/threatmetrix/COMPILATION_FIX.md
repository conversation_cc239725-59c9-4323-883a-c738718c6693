# ThreatMetrix Test Compilation Fix

## 问题描述

在 `ThreatMetrixServiceTest.java` 中，`threatMetrixCookieCutter.getName()` 方法调用报红，导致编译错误。

## 根本原因

`ThreatMetrixCookieCutter.getName()` 方法需要一个 `Env` 类型的参数，而不是 `String` 类型。

### 方法签名
```java
// CookieCutter.java
public final String getName(@Nonnull Env env) {
    return getName(getBaseName(), env);
}
```

### Env 枚举定义
```java
public enum Env {
    PROD,
    QA,
    DEV;
}
```

## 修复内容

### 1. 添加正确的导入
```java
import com.gumtree.common.properties.Env;
```

### 2. 修复 Mock 调用
**修复前:**
```java
when(threatMetrixCookieCutter.getName(anyString())).thenReturn("gt_tm");
verify(threatMetrixCookieCutter).getName(anyString());
```

**修复后:**
```java
when(threatMetrixCookieCutter.getName(any(Env.class))).thenReturn("gt_tm");
verify(threatMetrixCookieCutter).getName(any(Env.class));
```

### 3. 添加特定环境测试
```java
@Test
public void testProcessThreatMetrixForApiResponse_WhenEnabled_SpecificEnvironments() {
    // Test DEV environment
    when(threatMetrixCookieCutter.getName(eq(Env.DEV))).thenReturn("DEV_gt_tm");
    
    // Test PROD environment  
    when(threatMetrixCookieCutter.getName(eq(Env.PROD))).thenReturn("gt_tm");
}
```

## 修复的测试方法

1. `testProcessThreatMetrixForApiResponse_WhenEnabled_Success()`
2. `testProcessThreatMetrixForApiResponse_WhenEnabled_WithEnvironmentAwareCookieName()`
3. `testProcessThreatMetrixForApiResponse_WhenEnabled_NullSessionId()`
4. `testProcessThreatMetrixForApiResponse_WhenEnabled_EmptySessionId()`
5. `testProcessThreatMetrixForApiResponse_VerifyAllInteractions()`
6. `testProcessThreatMetrixForApiResponse_WhenEnabled_SpecificEnvironments()` (新增)

## 环境相关 Cookie 命名逻辑

根据 `CookieCutter.getName(String baseName, Env env)` 方法：

- **PROD**: `gt_tm`
- **DEV**: `DEV_gt_tm`  
- **QA**: `QA_gt_tm`

这确保了测试与实际的环境相关 Cookie 命名逻辑保持一致。

## 验证结果

✅ 所有编译错误已修复
✅ Mock 调用使用正确的参数类型
✅ 测试覆盖了不同环境场景
✅ 保持了与现有架构的兼容性

## 测试运行

```bash
# 运行 ThreatMetrix 服务测试
mvn test -Dtest=ThreatMetrixServiceTest

# 运行 ThreatMetrix 信息测试
mvn test -Dtest=ThreatMetrixInfoTest

# 运行所有 ThreatMetrix 相关测试
mvn test -Dtest=ThreatMetrix*
```

## 总结

这个修复确保了：
1. **编译正确性**: 使用正确的方法签名和参数类型
2. **测试完整性**: 覆盖了所有环境场景
3. **架构一致性**: 与现有的 Cookie 处理机制保持一致
4. **可维护性**: 清晰的测试结构和文档
