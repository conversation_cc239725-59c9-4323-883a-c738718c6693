#!/bin/bash

# Test script for ThreatMetrix service tests
echo "Running ThreatMetrix Service Tests..."

# Try to run the specific test class
mvn test -Dtest=ThreatMetrixServiceTest -q

if [ $? -eq 0 ]; then
    echo "✅ ThreatMetrixServiceTest passed!"
else
    echo "❌ ThreatMetrixServiceTest failed!"
fi

# Try to run the ThreatMetrixInfo test class
mvn test -Dtest=ThreatMetrixInfoTest -q

if [ $? -eq 0 ]; then
    echo "✅ ThreatMetrixInfoTest passed!"
else
    echo "❌ ThreatMetrixInfoTest failed!"
fi

echo "Test execution completed."
