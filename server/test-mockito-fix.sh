#!/bin/bash

echo "🔧 Testing Mockito Matcher Fix..."

# Run the specific test that was failing
echo "📝 Running the failing test method..."
cd server

mvn test -Dtest=ThreatMetrixServiceTest#testProcessThreatMetrixForApiResponse_WhenEnabled_Success -q

if [ $? -eq 0 ]; then
    echo "✅ testProcessThreatMetrixForApiResponse_WhenEnabled_Success passed!"
else
    echo "❌ testProcessThreatMetrixForApiResponse_WhenEnabled_Success still failing!"
    exit 1
fi

# Run all ThreatMetrix service tests
echo "📝 Running all ThreatMetrixServiceTest methods..."
mvn test -Dtest=ThreatMetrixServiceTest -q

if [ $? -eq 0 ]; then
    echo "✅ All ThreatMetrixServiceTest methods passed!"
else
    echo "❌ Some ThreatMetrixServiceTest methods still failing!"
    exit 1
fi

echo ""
echo "🎯 Mockito Matcher Fix Summary:"
echo "- ✅ Fixed InvalidUseOfMatchersException"
echo "- ✅ All parameters now use matchers consistently"
echo "- ✅ eq() used for specific values"
echo "- ✅ any() used for type matching"
echo "- ✅ All tests passing"

echo ""
echo "🚀 All tests are now working correctly!"
